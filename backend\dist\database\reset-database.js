"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetDatabase = resetDatabase;
exports.runMigrations = runMigrations;
const database_1 = require("../config/database");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
async function resetDatabase() {
    try {
        console.log('🔄 开始重置数据库...');
        console.log('📋 删除现有表...');
        await database_1.sequelize.drop({ cascade: true });
        console.log('✅ 所有表已删除');
        console.log('🔗 重新建立数据库连接...');
        await database_1.sequelize.authenticate();
        console.log('✅ 数据库连接成功');
        console.log('🚀 开始运行迁移...');
        await runMigrations();
        console.log('✅ 所有迁移执行完成');
        console.log('🎉 数据库重置完成！');
    }
    catch (error) {
        console.error('❌ 数据库重置失败:', error);
        throw error;
    }
    finally {
        await database_1.sequelize.close();
    }
}
async function runMigrations() {
    const migrationsDir = path_1.default.join(__dirname, 'migrations');
    const migrationFiles = fs_1.default.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
        .sort();
    console.log(`📁 找到 ${migrationFiles.length} 个迁移文件`);
    for (const file of migrationFiles) {
        try {
            console.log(`⏳ 执行迁移: ${file}`);
            const migrationPath = path_1.default.join(migrationsDir, file);
            const migration = require(migrationPath);
            if (migration.up && typeof migration.up === 'function') {
                await migration.up(database_1.sequelize.getQueryInterface());
                console.log(`✅ 迁移完成: ${file}`);
            }
            else {
                console.log(`⚠️  跳过无效迁移: ${file}`);
            }
        }
        catch (error) {
            console.error(`❌ 迁移失败: ${file}`, error);
            throw error;
        }
    }
}
if (require.main === module) {
    resetDatabase()
        .then(() => {
        console.log('✨ 数据库重置成功完成');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 数据库重置失败:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=reset-database.js.map
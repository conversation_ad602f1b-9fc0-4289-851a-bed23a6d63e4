import { Model, Optional } from 'sequelize';
export interface ArticleViewAttributes {
    id: number;
    articleId: number;
    userId?: number;
    ipAddress: string;
    userAgent?: string;
    viewedAt: Date;
}
export interface ArticleViewCreationAttributes extends Optional<ArticleViewAttributes, 'id' | 'userId' | 'userAgent' | 'viewedAt'> {
}
export declare class ArticleView extends Model<ArticleViewAttributes, ArticleViewCreationAttributes> implements ArticleViewAttributes {
    id: number;
    articleId: number;
    userId?: number;
    ipAddress: string;
    userAgent?: string;
    viewedAt: Date;
    static recordView(articleId: number, ipAddress: string, userId?: number, userAgent?: string): Promise<ArticleView>;
    static getArticleViewCount(articleId: number): Promise<number>;
    static getArticleUniqueViewers(articleId: number): Promise<number>;
    static getUserViewHistory(userId: number, limit?: number): Promise<ArticleView[]>;
    static getPopularArticles(limit?: number, days?: number): Promise<ArticleView[]>;
}
//# sourceMappingURL=ArticleView.d.ts.map
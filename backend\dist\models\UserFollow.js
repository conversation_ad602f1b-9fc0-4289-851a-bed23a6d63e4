"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFollow = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class UserFollow extends sequelize_1.Model {
    static async toggleFollow(followerId, followingId) {
        if (followerId === followingId) {
            throw new Error('不能关注自己');
        }
        const existingFollow = await this.findOne({
            where: { followerId, followingId }
        });
        if (existingFollow) {
            await existingFollow.destroy();
            const [followerCount, followingCount] = await Promise.all([
                this.count({ where: { followingId } }),
                this.count({ where: { followerId } })
            ]);
            return { following: false, followerCount, followingCount };
        }
        else {
            await this.create({ followerId, followingId });
            const [followerCount, followingCount] = await Promise.all([
                this.count({ where: { followingId } }),
                this.count({ where: { followerId } })
            ]);
            return { following: true, followerCount, followingCount };
        }
    }
    static async isFollowing(followerId, followingId) {
        const follow = await this.findOne({
            where: { followerId, followingId }
        });
        return !!follow;
    }
    static async getFollowerCount(userId) {
        return this.count({
            where: { followingId: userId }
        });
    }
    static async getFollowingCount(userId) {
        return this.count({
            where: { followerId: userId }
        });
    }
    static async getFollowers(userId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { followingId: userId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: database_1.sequelize.models.User,
                    as: 'follower',
                    attributes: ['id', 'username']
                }
            ]
        });
    }
    static async getFollowing(userId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { followerId: userId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: database_1.sequelize.models.User,
                    as: 'following',
                    attributes: ['id', 'username']
                }
            ]
        });
    }
    static async getMutualFollows(userId, limit = 20, offset = 0) {
        const following = await this.findAll({
            where: { followerId: userId },
            attributes: ['followingId']
        });
        const followingIds = following.map(f => f.followingId);
        if (followingIds.length === 0) {
            return { rows: [], count: 0 };
        }
        return this.findAndCountAll({
            where: {
                followerId: { [database_1.sequelize.Op.in]: followingIds },
                followingId: userId
            },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: database_1.sequelize.models.User,
                    as: 'follower',
                    attributes: ['id', 'username']
                }
            ]
        });
    }
    static async getRecommendedUsers(userId, limit = 10) {
        const userFollowing = await this.findAll({
            where: { followerId: userId },
            attributes: ['followingId']
        });
        const followingIds = userFollowing.map(f => f.followingId);
        if (followingIds.length === 0) {
            return [];
        }
        const recommendations = await this.findAll({
            where: {
                followerId: { [database_1.sequelize.Op.in]: followingIds },
                followingId: { [database_1.sequelize.Op.ne]: userId }
            },
            attributes: [
                'followingId',
                [database_1.sequelize.fn('COUNT', database_1.sequelize.col('id')), 'commonFollowers']
            ],
            group: ['followingId'],
            order: [[database_1.sequelize.literal('commonFollowers'), 'DESC']],
            limit,
            include: [
                {
                    model: database_1.sequelize.models.User,
                    as: 'following',
                    attributes: ['id', 'username']
                }
            ]
        });
        const alreadyFollowing = await this.findAll({
            where: { followerId: userId },
            attributes: ['followingId']
        });
        const alreadyFollowingIds = alreadyFollowing.map(f => f.followingId);
        return recommendations.filter(rec => !alreadyFollowingIds.includes(rec.followingId));
    }
}
exports.UserFollow = UserFollow;
UserFollow.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    followerId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'follower_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    followingId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'following_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'UserFollow',
    tableName: 'user_follows',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
        {
            unique: true,
            fields: ['follower_id', 'following_id']
        },
        {
            fields: ['follower_id']
        },
        {
            fields: ['following_id']
        },
        {
            fields: ['created_at']
        }
    ]
});
//# sourceMappingURL=UserFollow.js.map
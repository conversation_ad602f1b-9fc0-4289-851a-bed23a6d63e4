import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 用户设置模型的属性接口，定义了用户设置对象的基本字段结构
 */
export interface SettingsAttributes {
  id: number
  userId: number

  // 个人信息设置
  displayName?: string
  avatar?: string
  bio?: string
  website?: string
  location?: string

  // 偏好设置
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  itemsPerPage: number

  // 通知设置
  emailNotifications: boolean
  commentNotifications: boolean
  systemNotifications: boolean

  // 隐私设置
  profileVisibility: 'public' | 'private'
  defaultPostVisibility: 'public' | 'private'
  showEmail: boolean

  // 安全设置
  twoFactorEnabled: boolean

  createdAt: Date
  updatedAt: Date
}

/**
 * 用户设置创建时的属性接口，继承自 SettingsAttributes，但允许部分字段为空
 */
export interface SettingsCreationAttributes extends Optional<SettingsAttributes, 'id' | 'createdAt' | 'updatedAt'> { }

/**
 * 用户设置模型类，用于与数据库中的 settings 表进行交互
 * 实现了 SettingsAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class Settings extends Model<SettingsAttributes, SettingsCreationAttributes> implements SettingsAttributes {
  public id!: number
  public userId!: number

  // 个人信息设置
  public displayName?: string
  public avatar?: string
  public bio?: string
  public website?: string
  public location?: string

  // 偏好设置
  public theme!: 'light' | 'dark' | 'auto'
  public language!: string
  public timezone!: string
  public itemsPerPage!: number

  // 通知设置
  public emailNotifications!: boolean
  public commentNotifications!: boolean
  public systemNotifications!: boolean

  // 隐私设置
  public profileVisibility!: 'public' | 'private'
  public defaultPostVisibility!: 'public' | 'private'
  public showEmail!: boolean

  // 安全设置
  public twoFactorEnabled!: boolean

  public createdAt!: Date
  public updatedAt!: Date

  /**
   * 根据用户ID查找设置
   * @param userId - 用户ID
   * @returns 返回用户设置或null
   */
  public static async findByUserId(userId: number): Promise<Settings | null> {
    return this.findOne({ where: { userId } })
  }

  /**
   * 创建或更新用户设置
   * @param userId - 用户ID
   * @param settingsData - 设置数据
   * @returns 返回更新后的设置
   */
  public static async upsertByUserId(userId: number, settingsData: Partial<SettingsAttributes>): Promise<Settings> {
    const upsertData: any = {
      userId,
      ...settingsData
    }

    const [settings] = await this.upsert(upsertData)
    return settings
  }

  /**
   * 获取默认设置
   * @returns 返回默认设置对象
   */
  public static getDefaultSettings(): Partial<SettingsAttributes> {
    return {
      theme: 'auto',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      itemsPerPage: 10,
      emailNotifications: true,
      commentNotifications: true,
      systemNotifications: true,
      profileVisibility: 'public',
      defaultPostVisibility: 'public',
      showEmail: false,
      twoFactorEnabled: false
    }
  }

  /**
   * 验证设置数据
   * @param settingsData - 设置数据
   * @returns 返回验证结果
   */
  public static validateSettings(settingsData: Partial<SettingsAttributes>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证主题设置
    if (settingsData.theme && !['light', 'dark', 'auto'].includes(settingsData.theme)) {
      errors.push('主题设置必须是 light、dark 或 auto')
    }

    // 验证每页显示数量
    if (settingsData.itemsPerPage && (settingsData.itemsPerPage < 5 || settingsData.itemsPerPage > 100)) {
      errors.push('每页显示数量必须在 5-100 之间')
    }

    // 验证可见性设置
    if (settingsData.profileVisibility && !['public', 'private'].includes(settingsData.profileVisibility)) {
      errors.push('个人资料可见性必须是 public 或 private')
    }

    if (settingsData.defaultPostVisibility && !['public', 'private'].includes(settingsData.defaultPostVisibility)) {
      errors.push('默认文章可见性必须是 public 或 private')
    }

    // 验证网站URL格式
    if (settingsData.website && settingsData.website.trim()) {
      try {
        new URL(settingsData.website)
      } catch {
        errors.push('网站URL格式不正确')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换为JSON时隐藏敏感信息
   */
  public toJSON(): Omit<SettingsAttributes, never> {
    const values = { ...this.get() }
    return values
  }
}

/**
 * 初始化 Settings 模型，配置其字段、验证规则和生命周期钩子
 */
Settings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },

    // 个人信息设置
    displayName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'display_name',
      validate: {
        len: [1, 100]
      }
    },
    avatar: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: [0, 500]
      }
    },
    website: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [1, 100]
      }
    },

    // 偏好设置
    theme: {
      type: DataTypes.ENUM('light', 'dark', 'auto'),
      allowNull: false,
      defaultValue: 'auto'
    },
    language: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 'zh-CN',
      validate: {
        len: [2, 10]
      }
    },
    timezone: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'Asia/Shanghai',
      validate: {
        len: [1, 50]
      }
    },
    itemsPerPage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 10,
      field: 'items_per_page',
      validate: {
        min: 5,
        max: 100
      }
    },

    // 通知设置
    emailNotifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'email_notifications'
    },
    commentNotifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'comment_notifications'
    },
    systemNotifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'system_notifications'
    },

    // 隐私设置
    profileVisibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public',
      field: 'profile_visibility'
    },
    defaultPostVisibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public',
      field: 'default_post_visibility'
    },
    showEmail: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'show_email'
    },

    // 安全设置
    twoFactorEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'two_factor_enabled'
    },

    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Settings',
    tableName: 'settings',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id']
      }
    ]
  }
)

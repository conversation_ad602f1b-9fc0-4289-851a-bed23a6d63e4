import { Model, Optional } from 'sequelize';
export interface UserFollowAttributes {
    id: number;
    followerId: number;
    followingId: number;
    createdAt: Date;
}
export interface UserFollowCreationAttributes extends Optional<UserFollowAttributes, 'id' | 'createdAt'> {
}
export declare class UserFollow extends Model<UserFollowAttributes, UserFollowCreationAttributes> implements UserFollowAttributes {
    id: number;
    followerId: number;
    followingId: number;
    createdAt: Date;
    static toggleFollow(followerId: number, followingId: number): Promise<{
        following: boolean;
        followerCount: number;
        followingCount: number;
    }>;
    static isFollowing(followerId: number, followingId: number): Promise<boolean>;
    static getFollowerCount(userId: number): Promise<number>;
    static getFollowingCount(userId: number): Promise<number>;
    static getFollowers(userId: number, limit?: number, offset?: number): Promise<{
        rows: UserFollow[];
        count: number;
    }>;
    static getFollowing(userId: number, limit?: number, offset?: number): Promise<{
        rows: UserFollow[];
        count: number;
    }>;
    static getMutualFollows(userId: number, limit?: number, offset?: number): Promise<{
        rows: UserFollow[];
        count: number;
    }>;
    static getRecommendedUsers(userId: number, limit?: number): Promise<UserFollow[]>;
}
//# sourceMappingURL=UserFollow.d.ts.map
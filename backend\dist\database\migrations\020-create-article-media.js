"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_media', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        media_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'media',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('cover', 'content', 'gallery'),
            allowNull: false,
            defaultValue: 'content'
        },
        sort: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('article_media', ['article_id']);
    await queryInterface.addIndex('article_media', ['media_id']);
    await queryInterface.addIndex('article_media', ['article_id', 'type']);
    await queryInterface.addIndex('article_media', ['article_id', 'media_id'], {
        unique: true
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_media');
};
exports.down = down;
//# sourceMappingURL=020-create-article-media.js.map
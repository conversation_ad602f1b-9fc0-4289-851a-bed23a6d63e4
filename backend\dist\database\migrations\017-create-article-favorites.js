"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_favorites', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('article_favorites', ['article_id', 'user_id'], {
        unique: true,
        name: 'article_favorites_article_user_unique'
    });
    await queryInterface.addIndex('article_favorites', ['article_id'], {
        name: 'article_favorites_article_id_index'
    });
    await queryInterface.addIndex('article_favorites', ['user_id'], {
        name: 'article_favorites_user_id_index'
    });
    await queryInterface.addIndex('article_favorites', ['created_at'], {
        name: 'article_favorites_created_at_index'
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_favorites');
};
exports.down = down;
//# sourceMappingURL=017-create-article-favorites.js.map
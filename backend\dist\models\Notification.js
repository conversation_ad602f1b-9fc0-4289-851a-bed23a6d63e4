"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class Notification extends sequelize_1.Model {
    async markAsRead() {
        if (!this.isRead) {
            await this.update({
                isRead: true,
                readAt: new Date()
            });
        }
    }
    async markAsUnread() {
        if (this.isRead) {
            const updateData = {
                isRead: false
            };
            updateData.readAt = null;
            await this.update(updateData);
        }
    }
    getDisplayText() {
        return this.content || this.title;
    }
    isExpired() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return this.createdAt < thirtyDaysAgo;
    }
    getPriorityWeight() {
        switch (this.priority) {
            case 'high': return 3;
            case 'medium': return 2;
            case 'low': return 1;
            default: return 1;
        }
    }
    static async createInteractionNotification(data) {
        return await Notification.create({
            type: 'interaction',
            priority: 'medium',
            ...data
        });
    }
    static async createContentNotification(data) {
        return await Notification.create({
            type: 'content',
            priority: 'medium',
            ...data
        });
    }
    static async createSystemNotification(data) {
        return await Notification.create({
            type: 'system',
            priority: data.priority || 'high',
            relatedType: 'system',
            ...data
        });
    }
    static async createBulkNotifications(notifications) {
        return await Notification.bulkCreate(notifications);
    }
    static async getUnreadCount(userId) {
        return await Notification.count({
            where: {
                recipientId: userId,
                isRead: false
            }
        });
    }
    static async markBulkAsRead(notificationIds, userId) {
        const [affectedCount] = await Notification.update({
            isRead: true,
            readAt: new Date()
        }, {
            where: {
                id: notificationIds,
                recipientId: userId,
                isRead: false
            }
        });
        return affectedCount;
    }
    static async cleanupExpiredNotifications() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return await Notification.destroy({
            where: {
                createdAt: {
                    [sequelize_1.Op.lt]: thirtyDaysAgo
                },
                isRead: true
            }
        });
    }
}
exports.Notification = Notification;
Notification.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    type: {
        type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
        allowNull: false,
        validate: {
            isIn: [['interaction', 'content', 'system', 'marketing']]
        }
    },
    title: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: false,
        validate: {
            len: [1, 200],
            notEmpty: true
        }
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true
    },
    priority: {
        type: sequelize_1.DataTypes.ENUM('high', 'medium', 'low'),
        allowNull: false,
        defaultValue: 'medium',
        validate: {
            isIn: [['high', 'medium', 'low']]
        }
    },
    recipientId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'recipient_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    senderId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'sender_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    relatedType: {
        type: sequelize_1.DataTypes.ENUM('article', 'post', 'comment', 'user', 'system'),
        allowNull: true,
        field: 'related_type',
        validate: {
            isIn: [['article', 'post', 'comment', 'user', 'system']]
        }
    },
    relatedId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'related_id'
    },
    actionUrl: {
        type: sequelize_1.DataTypes.STRING(500),
        allowNull: true,
        field: 'action_url',
        validate: {
            len: [0, 500]
        }
    },
    isRead: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_read'
    },
    readAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'read_at'
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'updated_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Notification',
    tableName: 'notifications',
    timestamps: true,
    indexes: [
        {
            name: 'idx_recipient_created',
            fields: ['recipient_id', 'created_at']
        },
        {
            name: 'idx_recipient_unread',
            fields: ['recipient_id', 'is_read']
        },
        {
            name: 'idx_type_priority',
            fields: ['type', 'priority']
        },
        {
            name: 'idx_related',
            fields: ['related_type', 'related_id']
        }
    ]
});
//# sourceMappingURL=Notification.js.map
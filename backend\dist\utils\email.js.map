{"version": 3, "file": "email.js", "sourceRoot": "", "sources": ["../../src/utils/email.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAmC;AACnC,6DAAwD;AAqCxD,MAAa,YAAY;IAOvB,MAAM,CAAC,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC/E,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAA;gBACzF,OAAM;YACR,CAAC;YAED,IAAI,CAAC,MAAM,GAAG;gBACZ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;gBAC/C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;gBAC9C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;oBACjC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;iBAClC;aACF,CAAA;YAGD,IAAI,CAAC,WAAW,GAAG,oBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAG1D,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAA;YAC/B,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAA;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;YAC9D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,qCAAqC,EAAE,kBAAkB,CAAC,CAAA;YACnF,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAA;YACtF,CAAC;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,OAAqB;QAC1C,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YACzB,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE;oBACxD,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,OAAO,EAAE,OAAO,CAAC,OAAO;iBACzB,CAAC,CAAA;gBACF,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS;gBACpE,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;gBAClE,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAA;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YAC5D,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;YAC5D,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC/C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,KAAa,EAAE,QAAgB;QAC/E,MAAM,eAAe,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,uBAAuB,KAAK,EAAE,CAAA;QAEjF,MAAM,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC;YAC3C,QAAQ;YACR,eAAe;YACf,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,eAAe;SACjD,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC;YAC1B,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,UAAU;YACnB,IAAI;SACL,CAAC,CAAA;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,KAAa,EAAE,KAAa,EAAE,QAAgB;QAChF,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,yBAAyB,KAAK,EAAE,CAAA;QAE5E,MAAM,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC;YAC5C,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,eAAe;SACjD,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC;YAC1B,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,QAAQ;YACjB,IAAI;SACL,CAAC,CAAA;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,QAAgB;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACtC,QAAQ;YACR,QAAQ,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ;YAC7C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,eAAe;SACjD,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC;YAC1B,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,SAAS;YAClB,IAAI;SACL,CAAC,CAAA;IACJ,CAAC;IAKO,MAAM,CAAC,0BAA0B,CAAC,IAAuB;QAC/D,OAAO;;;;;;;;;;;;;;;;;;kBAkBO,IAAI,CAAC,OAAO;;;;qBAIT,IAAI,CAAC,QAAQ;uBACX,IAAI,CAAC,OAAO;;yBAEV,IAAI,CAAC,eAAe;;;gDAGG,IAAI,CAAC,eAAe;;;;;6BAKvC,IAAI,CAAC,OAAO;;;;;KAKpC,CAAA;IACH,CAAC;IAKO,MAAM,CAAC,2BAA2B,CAAC,IAAuB;QAChE,OAAO;;;;;;;;;;;;;;;;;;kBAkBO,IAAI,CAAC,OAAO;;;;qBAIT,IAAI,CAAC,QAAQ;;;yBAGT,IAAI,CAAC,QAAQ;;;gDAGU,IAAI,CAAC,QAAQ;;;;;6BAKhC,IAAI,CAAC,OAAO;;;;;KAKpC,CAAA;IACH,CAAC;IAKO,MAAM,CAAC,qBAAqB,CAAC,IAAuB;QAC1D,OAAO;;;;;;;;;;;;;;;;;;uBAkBY,IAAI,CAAC,OAAO;;;qBAGd,IAAI,CAAC,QAAQ;yBACT,IAAI,CAAC,OAAO;;;;;;;;yBAQZ,IAAI,CAAC,QAAQ;;;;uBAIf,IAAI,CAAC,OAAO;6BACN,IAAI,CAAC,OAAO;;;;;KAKpC,CAAA;IACH,CAAC;;AA1QH,oCA2QC;AA1QgB,wBAAW,GAAkC,IAAI,CAAA;AACjD,mBAAM,GAAuB,IAAI,CAAA"}
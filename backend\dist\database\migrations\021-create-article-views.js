"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_views', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        ip_address: {
            type: sequelize_1.DataTypes.STRING(45),
            allowNull: false
        },
        user_agent: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        viewed_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('article_views', ['article_id']);
    await queryInterface.addIndex('article_views', ['user_id']);
    await queryInterface.addIndex('article_views', ['ip_address']);
    await queryInterface.addIndex('article_views', ['viewed_at']);
    await queryInterface.addIndex('article_views', ['article_id', 'ip_address', 'viewed_at']);
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_views');
};
exports.down = down;
//# sourceMappingURL=021-create-article-views.js.map
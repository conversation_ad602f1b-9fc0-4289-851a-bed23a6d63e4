"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrationInfo = exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('notification_preferences', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
            allowNull: false,
            comment: '通知类型'
        },
        channel: {
            type: sequelize_1.DataTypes.ENUM('in_app', 'email', 'sms', 'push'),
            allowNull: false,
            comment: '通知渠道'
        },
        enabled: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否启用'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci'
    });
    await queryInterface.addIndex('notification_preferences', ['user_id'], {
        name: 'notification_preferences_user_id_index'
    });
    await queryInterface.addIndex('notification_preferences', ['type'], {
        name: 'notification_preferences_type_index'
    });
    await queryInterface.addIndex('notification_preferences', ['channel'], {
        name: 'notification_preferences_channel_index'
    });
    await queryInterface.addIndex('notification_preferences', ['user_id', 'type', 'channel'], {
        unique: true,
        name: 'notification_preferences_user_type_channel_unique'
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('notification_preferences');
};
exports.down = down;
exports.migrationInfo = {
    name: '20241229_create_notification_preferences_table',
    description: '创建通知偏好设置表',
    version: '1.0.0',
    author: 'System',
    createdAt: new Date('2024-12-29')
};
//# sourceMappingURL=016-create-notification-preferences.js.map
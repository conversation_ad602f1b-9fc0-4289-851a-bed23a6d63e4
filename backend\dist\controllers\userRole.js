"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUserRoles = exports.getUsersByRole = exports.checkUserRole = exports.checkUserPermission = exports.removeUserRole = exports.addUserRole = exports.assignUserRoles = exports.getUserPermissions = exports.getUserRoles = void 0;
const User_1 = require("../models/User");
const Role_1 = require("../models/Role");
const UserRole_1 = require("../models/UserRole");
const errorHandler_1 = require("../middleware/errorHandler");
const paramValidation_1 = require("../utils/paramValidation");
const getUserRoles = async (req, res, next) => {
    try {
        const { userId } = req.params;
        const user = await User_1.User.findByPk(userId);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const roles = await UserRole_1.UserRole.getUserRoles(userIdNum);
        res.json({
            success: true,
            data: {
                userId: userIdNum,
                username: user.username,
                roles
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserRoles = getUserRoles;
const getUserPermissions = async (req, res, next) => {
    try {
        const { userId } = req.params;
        const user = await User_1.User.findByPk(userId);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const permissions = await UserRole_1.UserRole.getUserPermissions(userIdNum);
        const groupedPermissions = {};
        permissions.forEach(permission => {
            const resource = permission.resource;
            if (!groupedPermissions[resource]) {
                groupedPermissions[resource] = [];
            }
            groupedPermissions[resource].push(permission);
        });
        res.json({
            success: true,
            data: {
                userId: userIdNum,
                username: user.username,
                permissions,
                groupedPermissions
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserPermissions = getUserPermissions;
const assignUserRoles = async (req, res, next) => {
    try {
        const { userId } = req.params;
        const { roleIds } = req.body;
        const assignedBy = req.user?.id;
        const user = await User_1.User.findByPk(userId);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        if (roleIds && roleIds.length > 0) {
            const validRoles = await Role_1.Role.findAll({
                where: { id: roleIds, isActive: true }
            });
            if (validRoles.length !== roleIds.length) {
                throw (0, errorHandler_1.createError)(400, '包含无效的角色ID', 'INVALID_ROLE_IDS');
            }
        }
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        await UserRole_1.UserRole.assignRoles(userIdNum, roleIds || [], assignedBy);
        const roles = await UserRole_1.UserRole.getUserRoles(userIdNum);
        res.json({
            success: true,
            data: {
                userId: userIdNum,
                username: user.username,
                roles
            },
            message: '角色分配成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.assignUserRoles = assignUserRoles;
const addUserRole = async (req, res, next) => {
    try {
        const { userId, roleId } = req.params;
        const assignedBy = req.user?.id;
        const user = await User_1.User.findByPk(userId);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const role = await Role_1.Role.findOne({
            where: { id: roleId, isActive: true }
        });
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在或已禁用', 'ROLE_NOT_FOUND');
        }
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const roleIdNum = (0, paramValidation_1.getRoleIdParam)(req);
        const hasRole = await UserRole_1.UserRole.hasRole(userIdNum, roleIdNum);
        if (hasRole) {
            throw (0, errorHandler_1.createError)(400, '用户已经拥有该角色', 'USER_ALREADY_HAS_ROLE');
        }
        await UserRole_1.UserRole.assignRole(userIdNum, roleIdNum, assignedBy);
        res.json({
            success: true,
            message: '角色添加成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.addUserRole = addUserRole;
const removeUserRole = async (req, res, next) => {
    try {
        const { userId, roleId } = req.params;
        const user = await User_1.User.findByPk(userId);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const role = await Role_1.Role.findByPk(roleId);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const roleIdNum = (0, paramValidation_1.getRoleIdParam)(req);
        const hasRole = await UserRole_1.UserRole.hasRole(userIdNum, roleIdNum);
        if (!hasRole) {
            throw (0, errorHandler_1.createError)(400, '用户没有该角色', 'USER_DOES_NOT_HAVE_ROLE');
        }
        await UserRole_1.UserRole.removeRole(userIdNum, roleIdNum);
        res.json({
            success: true,
            message: '角色移除成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.removeUserRole = removeUserRole;
const checkUserPermission = async (req, res, next) => {
    try {
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const permissionName = (0, paramValidation_1.getStringParam)(req, 'permissionName');
        const user = await User_1.User.findByPk(userIdNum);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const hasPermission = await UserRole_1.UserRole.hasPermission(userIdNum, permissionName);
        res.json({
            success: true,
            data: {
                userId: userIdNum,
                username: user.username,
                permissionName,
                hasPermission
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.checkUserPermission = checkUserPermission;
const checkUserRole = async (req, res, next) => {
    try {
        const userIdNum = (0, paramValidation_1.getUserIdParam)(req);
        const roleName = (0, paramValidation_1.getStringParam)(req, 'roleName');
        const user = await User_1.User.findByPk(userIdNum);
        if (!user) {
            throw (0, errorHandler_1.createError)(404, '用户不存在', 'USER_NOT_FOUND');
        }
        const hasRole = await UserRole_1.UserRole.hasRoleByName(userIdNum, roleName);
        res.json({
            success: true,
            data: {
                userId: userIdNum,
                username: user.username,
                roleName,
                hasRole
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.checkUserRole = checkUserRole;
const getUsersByRole = async (req, res, next) => {
    try {
        const { roleId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const role = await Role_1.Role.findByPk(roleId);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        const { rows: userRoles, count: total } = await UserRole_1.UserRole.findAndCountAll({
            where: { roleId: (0, paramValidation_1.getRoleIdParam)(req) },
            limit: limitNum,
            offset,
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'username', 'email', 'createdAt']
                },
                {
                    model: User_1.User,
                    as: 'assigner',
                    attributes: ['id', 'username'],
                    required: false
                }
            ],
            order: [['assignedAt', 'DESC']]
        });
        res.json({
            success: true,
            data: {
                role: {
                    id: role.id,
                    name: role.name,
                    description: role.description
                },
                users: userRoles,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    pages: Math.ceil(total / limitNum)
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUsersByRole = getUsersByRole;
const getCurrentUserRoles = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            throw (0, errorHandler_1.createError)(401, '需要登录', 'AUTHENTICATION_REQUIRED');
        }
        const roles = await UserRole_1.UserRole.getUserRoles(userId);
        const permissions = await UserRole_1.UserRole.getUserPermissions(userId);
        const groupedPermissions = {};
        permissions.forEach(permission => {
            const resource = permission.resource;
            if (!groupedPermissions[resource]) {
                groupedPermissions[resource] = [];
            }
            groupedPermissions[resource].push(permission);
        });
        res.json({
            success: true,
            data: {
                userId,
                roles,
                permissions,
                groupedPermissions
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUserRoles = getCurrentUserRoles;
//# sourceMappingURL=userRole.js.map
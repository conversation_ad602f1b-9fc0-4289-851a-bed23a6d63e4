"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleMedia = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class ArticleMedia extends sequelize_1.Model {
    static async findByArticleId(articleId, type) {
        const whereClause = { articleId };
        if (type) {
            whereClause.type = type;
        }
        return this.findAll({
            where: whereClause,
            order: [['sort', 'ASC'], ['createdAt', 'ASC']]
        });
    }
    static async findByMediaId(mediaId) {
        return this.findAll({
            where: { mediaId },
            order: [['createdAt', 'DESC']]
        });
    }
}
exports.ArticleMedia = ArticleMedia;
ArticleMedia.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    mediaId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'media_id',
        references: {
            model: 'media',
            key: 'id'
        }
    },
    type: {
        type: sequelize_1.DataTypes.ENUM('cover', 'content', 'gallery'),
        allowNull: false,
        defaultValue: 'content'
    },
    sort: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'ArticleMedia',
    tableName: 'article_media',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
        {
            fields: ['article_id']
        },
        {
            fields: ['media_id']
        },
        {
            fields: ['article_id', 'type']
        },
        {
            unique: true,
            fields: ['article_id', 'media_id']
        }
    ]
});
//# sourceMappingURL=ArticleMedia.js.map
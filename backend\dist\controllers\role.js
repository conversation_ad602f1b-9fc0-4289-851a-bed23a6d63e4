"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoleUsers = exports.assignRolePermissions = exports.getRolePermissions = exports.deleteRole = exports.updateRole = exports.createRole = exports.getRoleById = exports.getRoles = void 0;
const Role_1 = require("../models/Role");
const Permission_1 = require("../models/Permission");
const RolePermission_1 = require("../models/RolePermission");
const UserRole_1 = require("../models/UserRole");
const errorHandler_1 = require("../middleware/errorHandler");
const paramValidation_1 = require("../utils/paramValidation");
const getRoles = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, search, isActive } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const whereClause = {};
        if (search) {
            whereClause.name = { [require('sequelize').Op.like]: `%${search}%` };
        }
        if (isActive !== undefined) {
            whereClause.isActive = isActive === 'true';
        }
        const { rows: roles, count: total } = await Role_1.Role.findAndCountAll({
            where: whereClause,
            limit: limitNum,
            offset,
            order: [['createdAt', 'DESC']],
            include: [
                {
                    model: Permission_1.Permission,
                    as: 'permissions',
                    through: { attributes: [] }
                }
            ]
        });
        const rolesWithStats = await Promise.all(roles.map(async (role) => {
            const userCount = await role.getUserCount();
            const permissionCount = await role.getPermissionCount();
            return {
                ...role.toJSON(),
                userCount,
                permissionCount
            };
        }));
        res.json({
            success: true,
            data: {
                roles: rolesWithStats,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    pages: Math.ceil(total / limitNum)
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getRoles = getRoles;
const getRoleById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const role = await Role_1.Role.findByPk(id, {
            include: [
                {
                    model: Permission_1.Permission,
                    as: 'permissions',
                    through: { attributes: ['assignedAt', 'assignedBy'] }
                }
            ]
        });
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        const userCount = await role.getUserCount();
        const permissionCount = await role.getPermissionCount();
        res.json({
            success: true,
            data: {
                ...role.toJSON(),
                userCount,
                permissionCount
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getRoleById = getRoleById;
const createRole = async (req, res, next) => {
    try {
        const { name, description, isActive = true, permissionIds = [] } = req.body;
        const existingRole = await Role_1.Role.findByName(name);
        if (existingRole) {
            throw (0, errorHandler_1.createError)(400, '角色名称已存在', 'ROLE_NAME_EXISTS');
        }
        const role = await Role_1.Role.create({
            name,
            description,
            isActive,
            isSystem: false
        });
        if (permissionIds.length > 0) {
            await role.assignPermissions(permissionIds);
        }
        const createdRole = await Role_1.Role.findByPk(role.id, {
            include: [
                {
                    model: Permission_1.Permission,
                    as: 'permissions',
                    through: { attributes: [] }
                }
            ]
        });
        res.status(201).json({
            success: true,
            data: createdRole,
            message: '角色创建成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createRole = createRole;
const updateRole = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, isActive, permissionIds } = req.body;
        const role = await Role_1.Role.findByPk(id);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        if (role.isSystem && (name !== role.name || isActive === false)) {
            throw (0, errorHandler_1.createError)(400, '系统角色不能修改名称或禁用', 'SYSTEM_ROLE_IMMUTABLE');
        }
        if (name && name !== role.name) {
            const existingRole = await Role_1.Role.findByName(name);
            if (existingRole) {
                throw (0, errorHandler_1.createError)(400, '角色名称已存在', 'ROLE_NAME_EXISTS');
            }
        }
        await role.update({
            name: name || role.name,
            description: description !== undefined ? description : role.description,
            isActive: isActive !== undefined ? isActive : role.isActive
        });
        if (permissionIds !== undefined) {
            await role.assignPermissions(permissionIds);
        }
        const updatedRole = await Role_1.Role.findByPk(role.id, {
            include: [
                {
                    model: Permission_1.Permission,
                    as: 'permissions',
                    through: { attributes: [] }
                }
            ]
        });
        res.json({
            success: true,
            data: updatedRole,
            message: '角色更新成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateRole = updateRole;
const deleteRole = async (req, res, next) => {
    try {
        const { id } = req.params;
        const role = await Role_1.Role.findByPk(id);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        if (role.isSystem) {
            throw (0, errorHandler_1.createError)(400, '系统角色不能删除', 'SYSTEM_ROLE_UNDELETABLE');
        }
        const userCount = await role.getUserCount();
        if (userCount > 0) {
            throw (0, errorHandler_1.createError)(400, `角色正在被 ${userCount} 个用户使用，无法删除`, 'ROLE_IN_USE');
        }
        await role.destroy();
        res.json({
            success: true,
            message: '角色删除成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteRole = deleteRole;
const getRolePermissions = async (req, res, next) => {
    try {
        const { id } = req.params;
        const role = await Role_1.Role.findByPk(id);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        const permissions = await role.getPermissions();
        res.json({
            success: true,
            data: permissions
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getRolePermissions = getRolePermissions;
const assignRolePermissions = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { permissionIds } = req.body;
        const assignedBy = req.user?.id;
        const role = await Role_1.Role.findByPk(id);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        if (permissionIds && permissionIds.length > 0) {
            const validPermissions = await Permission_1.Permission.findAll({
                where: { id: permissionIds, isActive: true }
            });
            if (validPermissions.length !== permissionIds.length) {
                throw (0, errorHandler_1.createError)(400, '包含无效的权限ID', 'INVALID_PERMISSION_IDS');
            }
        }
        await RolePermission_1.RolePermission.assignPermissions(role.id, permissionIds || [], assignedBy);
        const permissions = await role.getPermissions();
        res.json({
            success: true,
            data: permissions,
            message: '权限分配成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.assignRolePermissions = assignRolePermissions;
const getRoleUsers = async (req, res, next) => {
    try {
        const roleId = (0, paramValidation_1.getIdParam)(req);
        const { limit: limitNum, offset: offsetNum, page: pageNum } = (0, paramValidation_1.getPaginationParams)(req);
        const role = await Role_1.Role.findByPk(roleId);
        if (!role) {
            throw (0, errorHandler_1.createError)(404, '角色不存在', 'ROLE_NOT_FOUND');
        }
        const userRoles = await UserRole_1.UserRole.findAll({
            where: { roleId },
            limit: limitNum,
            offset: offsetNum,
            include: ['user', 'assigner']
        });
        const total = await UserRole_1.UserRole.count({ where: { roleId } });
        res.json({
            success: true,
            data: {
                users: userRoles,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    pages: Math.ceil(total / limitNum)
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getRoleUsers = getRoleUsers;
//# sourceMappingURL=role.js.map
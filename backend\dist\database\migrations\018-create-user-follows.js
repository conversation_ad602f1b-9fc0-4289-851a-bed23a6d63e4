"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('user_follows', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        follower_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        following_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('user_follows', ['follower_id', 'following_id'], {
        unique: true,
        name: 'user_follows_follower_following_unique'
    });
    await queryInterface.addIndex('user_follows', ['follower_id'], {
        name: 'user_follows_follower_id_index'
    });
    await queryInterface.addIndex('user_follows', ['following_id'], {
        name: 'user_follows_following_id_index'
    });
    await queryInterface.addIndex('user_follows', ['created_at'], {
        name: 'user_follows_created_at_index'
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('user_follows');
};
exports.down = down;
//# sourceMappingURL=018-create-user-follows.js.map
{"version": 3, "file": "016-create-notification-preferences.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/016-create-notification-preferences.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,0BAA0B,EAAE;QAC3D,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrE,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC;YACtD,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,CAAC,EAAE;QACrE,IAAI,EAAE,wCAAwC;KAC/C,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,MAAM,CAAC,EAAE;QAClE,IAAI,EAAE,qCAAqC;KAC5C,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,CAAC,EAAE;QACrE,IAAI,EAAE,wCAAwC;KAC/C,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;QACxF,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,mDAAmD;KAC1D,CAAC,CAAA;AACJ,CAAC,CAAA;AAlEY,QAAA,EAAE,MAkEd;AAKM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;AAC5D,CAAC,CAAA;AAFY,QAAA,IAAI,QAEhB;AAKY,QAAA,aAAa,GAAG;IAC3B,IAAI,EAAE,gDAAgD;IACtD,WAAW,EAAE,WAAW;IACxB,OAAO,EAAE,OAAO;IAChB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;CAClC,CAAA"}
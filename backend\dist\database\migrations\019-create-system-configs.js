"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('system_configs', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        key: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '配置键名'
        },
        value: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '配置值'
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('string', 'number', 'boolean', 'json'),
            allowNull: false,
            defaultValue: 'string',
            comment: '配置值类型'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '配置描述'
        },
        category: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            defaultValue: 'general',
            comment: '配置分类'
        },
        is_public: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否公开（前端可访问）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('system_configs', ['key'], {
        unique: true,
        name: 'system_configs_key_unique'
    });
    await queryInterface.addIndex('system_configs', ['category'], {
        name: 'system_configs_category_index'
    });
    await queryInterface.addIndex('system_configs', ['is_public'], {
        name: 'system_configs_is_public_index'
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('system_configs');
};
exports.down = down;
//# sourceMappingURL=019-create-system-configs.js.map
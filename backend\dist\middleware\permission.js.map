{"version": 3, "file": "permission.js", "sourceRoot": "", "sources": ["../../src/middleware/permission.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAC7C,iDAA4C;AAmBrC,MAAM,iBAAiB,GAAG,CAAC,cAAsB,EAAE,EAAE;IAC1D,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;YAE1E,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,cAAc,EAAE,EAAE,mBAAmB,CAAC,CAAA;YACxE,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAtBY,QAAA,iBAAiB,qBAsB7B;AAQM,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAE,EAAE;IAC9C,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;YAE9D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,EAAE,eAAe,CAAC,CAAA;YAC9D,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAtBY,QAAA,WAAW,eAsBvB;AAOM,MAAM,qBAAqB,GAAG,CAAC,eAAyB,EAAE,EAAE;IACjE,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;gBAC1E,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,cAAc,EAAE,EAAE,mBAAmB,CAAC,CAAA;gBACxE,CAAC;YACH,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAvBY,QAAA,qBAAqB,yBAuBjC;AAOM,MAAM,oBAAoB,GAAG,CAAC,eAAyB,EAAE,EAAE;IAChE,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,IAAI,gBAAgB,GAAG,KAAK,CAAA;YAC5B,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;gBAC1E,IAAI,aAAa,EAAE,CAAC;oBAClB,gBAAgB,GAAG,IAAI,CAAA;oBACvB,MAAK;gBACP,CAAC;YACH,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAA;YACxF,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AA7BY,QAAA,oBAAoB,wBA6BhC;AAOM,MAAM,eAAe,GAAG,CAAC,SAAmB,EAAE,EAAE;IACrD,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;gBAC9D,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,EAAE,eAAe,CAAC,CAAA;gBAC9D,CAAC;YACH,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAvBY,QAAA,eAAe,mBAuB3B;AAOM,MAAM,cAAc,GAAG,CAAC,SAAmB,EAAE,EAAE;IACpD,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,IAAI,UAAU,GAAG,KAAK,CAAA;YACtB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;gBAC9D,IAAI,OAAO,EAAE,CAAC;oBACZ,UAAU,GAAG,IAAI,CAAA;oBACjB,MAAK;gBACP,CAAC;YACH,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,eAAe,CAAC,CAAA;YAC9E,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AA7BY,QAAA,cAAc,kBA6B1B;AAMY,QAAA,iBAAiB,GAAG,IAAA,mBAAW,EAAC,aAAa,CAAC,CAAA;AAM9C,QAAA,YAAY,GAAG,IAAA,sBAAc,EAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAA;AAMvD,QAAA,aAAa,GAAG,IAAA,sBAAc,EAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAA;AASxE,MAAM,4BAA4B,GAAG,CAC1C,kBAAyE,EACzE,eAAwB,EACxB,EAAE;IACF,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAG1B,MAAM,eAAe,GAAG,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA;YAGrD,IAAI,eAAe,KAAK,MAAM,EAAE,CAAC;gBAC/B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC;YAGD,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,kBAAkB,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;gBAChF,IAAI,kBAAkB,EAAE,CAAC;oBACvB,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC;YACH,CAAC;YAGD,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,mBAAmB,EAAE,kCAAkC,CAAC,CAAA;QACjF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAnCY,QAAA,4BAA4B,gCAmCxC;AAOM,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAqB,EAAE;IAC5E,MAAM,WAAW,GAAG,MAAM,mBAAQ,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;IAC7D,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA;AAHY,QAAA,kBAAkB,sBAG9B;AAOM,MAAM,YAAY,GAAG,KAAK,EAAE,MAAc,EAAqB,EAAE;IACtE,MAAM,KAAK,GAAG,MAAM,mBAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;IACjD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;AAC/B,CAAC,CAAA;AAHY,QAAA,YAAY,gBAGxB;AAUM,MAAM,4BAA4B,GAAG,CAC1C,cAAsB,EACtB,SAAiB,EACjB,UAAkB,EAClB,EAAE;IACF,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC5E,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;YACjD,CAAC;YAGD,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,cAAc,CAAC,CAAA;YAC/E,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,IAAI,EAAE,CAAA;YACf,CAAC;YAKD,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC,CAAA;YACjD,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBAGnB,OAAO,IAAI,EAAE,CAAA;YACf,CAAC;YAED,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,EAAE,yBAAyB,CAAC,CAAA;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAhCY,QAAA,4BAA4B,gCAgCxC"}
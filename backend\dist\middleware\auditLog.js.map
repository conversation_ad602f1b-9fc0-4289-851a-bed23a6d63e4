{"version": 3, "file": "auditLog.js", "sourceRoot": "", "sources": ["../../src/middleware/auditLog.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,iDAA6C;AAC7C,8DAAqF;AAqCrF,MAAM,WAAW,GAAG,CAAC,GAAY,EAAU,EAAE;IAC3C,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACnD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;IAEvC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;QACrC,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,SAAS,CAAA;IACxD,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QACnD,OAAO,YAAY,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAA;IACf,CAAC;IAED,OAAO,GAAG,CAAC,MAAM,EAAE,aAAa,IAAI,GAAG,CAAC,EAAE,IAAI,SAAS,CAAA;AACzD,CAAC,CAAA;AAOD,MAAM,YAAY,GAAG,CAAC,GAAY,EAAsB,EAAE;IAExD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;IAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACnD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACpC,CAAC;IACD,OAAO,SAAS,CAAA;AAClB,CAAC,CAAA;AAOM,MAAM,wBAAwB,GAAG,CAAC,MAAsB,EAAE,EAAE;IACjE,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtD,OAAO,IAAI,EAAE,CAAA;YACf,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAE5B,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;YAG/E,GAAG,CAAC,SAAS,GAAG;gBACd,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,EAAE,UAAU,EAAE,CAAC;gBAC/C,SAAS;aACV,CAAA;YAGD,IAAI,MAAM,CAAC,cAAc,IAAI,UAAU,EAAE,CAAC;gBACxC,IAAI,CAAC;oBAGH,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;gBAC5E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;gBACpD,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAGvC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;gBAE5B,YAAY,CAAC,KAAK,IAAI,EAAE;oBACtB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;oBAC9C,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;oBACrD,CAAC;gBACH,CAAC,CAAC,CAAA;gBAGF,OAAO,YAAY,CAAC,IAAI,CAAC,CAAA;YAC3B,CAAC,CAAA;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC/C,IAAI,EAAE,CAAA;QACR,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAvDY,QAAA,wBAAwB,4BAuDpC;AASD,MAAM,cAAc,GAAG,KAAK,EAC1B,GAAyB,EACzB,GAAa,EACb,MAAsB,EACtB,YAAiB,EACF,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC1B,MAAM,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;QAGtE,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;QAGnF,IAAI,OAA2B,CAAA;QAC/B,IAAI,MAAM,CAAC,cAAc,IAAI,YAAY,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAClE,IAAI,CAAC;gBAEH,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;oBACtB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;gBAC7C,CAAC;qBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;oBAC5C,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACtD,CAAC;QACH,CAAC;QAGD,IAAI,YAAgC,CAAA;QACpC,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YACxB,IAAI,YAAY,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACrE,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAA;YAC3C,CAAC;iBAAM,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBAChD,YAAY,GAAG,YAAY,CAAC,OAAO,CAAA;YACrC,CAAC;iBAAM,CAAC;gBACN,YAAY,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAE,CAAA;YACzC,CAAC;QACH,CAAC;QAGD,MAAM,OAAO,GAAQ;YACnB,MAAM,EAAE,GAAG,CAAC,SAAS,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM;YAC9C,QAAQ,EAAE,GAAG,CAAC,SAAS,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ;YACpD,SAAS,EAAE,WAAW,CAAC,GAAG,CAAC;YAC3B,MAAM;YACN,QAAQ;SACT,CAAA;QAGD,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,SAAS;YAAE,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;QAC5D,IAAI,GAAG,CAAC,SAAS,EAAE,UAAU,KAAK,SAAS;YAAE,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,CAAA;QAC1F,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO;YAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACnF,IAAI,OAAO;YAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QACtC,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YAAE,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QAC5E,IAAI,YAAY,CAAC,GAAG,CAAC;YAAE,OAAO,CAAC,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;QAC5D,IAAI,YAAY;YAAE,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAErD,MAAM,mBAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;IACrD,CAAC;AACH,CAAC,CAAA;AAQD,MAAM,eAAe,GAAG,KAAK,EAAE,QAAgB,EAAE,UAAkB,EAAgB,EAAE;IACnF,IAAI,CAAC;QAEH,MAAM,MAAM,GAAG,wDAAa,WAAW,GAAC,CAAA;QAExC,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,KAAK,MAAM;gBACT,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;oBAClD,UAAU,EAAE,EAAE,OAAO,EAAE,CAAC,cAAc,CAAC,EAAE;iBAC1C,CAAC,CAAA;gBACF,OAAO,IAAI,EAAE,MAAM,EAAE,CAAA;YAEvB,KAAK,SAAS;gBACZ,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACzD,OAAO,OAAO,EAAE,MAAM,EAAE,CAAA;YAE1B,KAAK,MAAM;gBACT,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACnD,OAAO,IAAI,EAAE,MAAM,EAAE,CAAA;YAEvB,KAAK,SAAS;gBACZ,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACzD,OAAO,OAAO,EAAE,MAAM,EAAE,CAAA;YAE1B,KAAK,UAAU;gBACb,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBAC3D,OAAO,QAAQ,EAAE,MAAM,EAAE,CAAA;YAE3B,KAAK,KAAK;gBACR,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACjD,OAAO,GAAG,EAAE,MAAM,EAAE,CAAA;YAEtB,KAAK,MAAM;gBACT,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACnD,OAAO,IAAI,EAAE,MAAM,EAAE,CAAA;YAEvB,KAAK,YAAY;gBACf,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBAC/D,OAAO,UAAU,EAAE,MAAM,EAAE,CAAA;YAE7B;gBACE,OAAO,IAAI,CAAA;QACf,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,iBAAiB,QAAQ,QAAQ,EAAE,KAAK,CAAC,CAAA;QACtD,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAOY,QAAA,eAAe,GAAG,IAAA,gCAAwB,EAAC;IACtD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,MAAM;IAChB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEW,QAAA,eAAe,GAAG,IAAA,gCAAwB,EAAC;IACtD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,MAAM;IAChB,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,4BAAU,EAAC,GAAG,CAAC;IACvC,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEW,QAAA,eAAe,GAAG,IAAA,gCAAwB,EAAC;IACtD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,MAAM;IAChB,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,4BAAU,EAAC,GAAG,CAAC;IACvC,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAGW,QAAA,kBAAkB,GAAG,IAAA,gCAAwB,EAAC;IACzD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,SAAS;IACnB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEW,QAAA,kBAAkB,GAAG,IAAA,gCAAwB,EAAC;IACzD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,SAAS;IACnB,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,4BAAU,EAAC,GAAG,CAAC;IACvC,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEW,QAAA,kBAAkB,GAAG,IAAA,gCAAwB,EAAC;IACzD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,SAAS;IACnB,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,4BAAU,EAAC,GAAG,CAAC;IACvC,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAGW,QAAA,UAAU,GAAG,IAAA,gCAAwB,EAAC;IACjD,MAAM,EAAE,OAAO;IACf,QAAQ,EAAE,MAAM;IAChB,cAAc,EAAE,KAAK;CACtB,CAAC,CAAA;AAGW,QAAA,WAAW,GAAG,IAAA,gCAAwB,EAAC;IAClD,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,MAAM;IAChB,cAAc,EAAE,KAAK;CACtB,CAAC,CAAA;AAGW,QAAA,eAAe,GAAG,IAAA,gCAAwB,EAAC;IACtD,MAAM,EAAE,aAAa;IACrB,QAAQ,EAAE,WAAW;IACrB,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,gCAAc,EAAC,GAAG,CAAC;IAC3C,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEW,QAAA,oBAAoB,GAAG,IAAA,gCAAwB,EAAC;IAC3D,MAAM,EAAE,kBAAkB;IAC1B,QAAQ,EAAE,iBAAiB;IAC3B,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,gCAAc,EAAC,GAAG,CAAC;IAC3C,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAA;AAEF,kBAAe;IACb,wBAAwB,EAAxB,gCAAwB;IACxB,eAAe,EAAf,uBAAe;IACf,eAAe,EAAf,uBAAe;IACf,eAAe,EAAf,uBAAe;IACf,kBAAkB,EAAlB,0BAAkB;IAClB,kBAAkB,EAAlB,0BAAkB;IAClB,kBAAkB,EAAlB,0BAAkB;IAClB,UAAU,EAAV,kBAAU;IACV,WAAW,EAAX,mBAAW;IACX,eAAe,EAAf,uBAAe;IACf,oBAAoB,EAApB,4BAAoB;CACrB,CAAA"}
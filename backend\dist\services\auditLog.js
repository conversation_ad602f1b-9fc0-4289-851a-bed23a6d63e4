"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogService = void 0;
const AuditLog_1 = require("../models/AuditLog");
const User_1 = require("../models/User");
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class AuditLogService {
    static async createLog(logData) {
        try {
            return await AuditLog_1.AuditLog.logAction(logData);
        }
        catch (error) {
            console.error('Failed to create audit log:', error);
            throw new Error('Failed to create audit log');
        }
    }
    static async queryLogs(options) {
        try {
            const { logs, total } = await AuditLog_1.AuditLog.queryLogs(options);
            const page = options.page || 1;
            const limit = options.limit || 20;
            const totalPages = Math.ceil(total / limit);
            return {
                logs,
                total,
                page,
                limit,
                totalPages
            };
        }
        catch (error) {
            console.error('Failed to query audit logs:', error);
            throw new Error('Failed to query audit logs');
        }
    }
    static async getUserLogs(userId, options) {
        return this.queryLogs({ ...options, userId });
    }
    static async getDetailedStats(options = {}) {
        try {
            const { userId, startDate, endDate, resource, action } = options;
            const baseWhere = {};
            if (userId !== undefined)
                baseWhere.userId = userId;
            if (resource)
                baseWhere.resource = resource;
            if (action)
                baseWhere.action = action;
            if (startDate || endDate) {
                baseWhere.createdAt = {};
                if (startDate)
                    baseWhere.createdAt[sequelize_1.Op.gte] = startDate;
                if (endDate)
                    baseWhere.createdAt[sequelize_1.Op.lte] = endDate;
            }
            const [totalLogs, successLogs, failedLogs, pendingLogs] = await Promise.all([
                AuditLog_1.AuditLog.count({ where: baseWhere }),
                AuditLog_1.AuditLog.count({ where: { ...baseWhere, status: 'success' } }),
                AuditLog_1.AuditLog.count({ where: { ...baseWhere, status: 'failed' } }),
                AuditLog_1.AuditLog.count({ where: { ...baseWhere, status: 'pending' } })
            ]);
            const actionStatsQuery = await database_1.sequelize.query(`
        SELECT action, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY action
        ORDER BY count DESC
        LIMIT 10
      `, {
                type: sequelize_1.QueryTypes.SELECT,
                replacements: this.buildReplacements(baseWhere)
            });
            const actionStats = {};
            const topActions = actionStatsQuery.map(item => ({
                action: item.action,
                count: parseInt(item.count)
            }));
            topActions.forEach(item => {
                actionStats[item.action] = item.count;
            });
            const resourceStatsQuery = await database_1.sequelize.query(`
        SELECT resource, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY resource
        ORDER BY count DESC
        LIMIT 10
      `, {
                type: sequelize_1.QueryTypes.SELECT,
                replacements: this.buildReplacements(baseWhere)
            });
            const resourceStats = {};
            const topResources = resourceStatsQuery.map(item => ({
                resource: item.resource,
                count: parseInt(item.count)
            }));
            topResources.forEach(item => {
                resourceStats[item.resource] = item.count;
            });
            let userStats = [];
            if (userId === undefined) {
                const userStatsQuery = await database_1.sequelize.query(`
          SELECT al.user_id, u.username, COUNT(*) as count
          FROM audit_logs al
          LEFT JOIN users u ON al.user_id = u.id
          WHERE ${this.buildWhereClause(baseWhere)}
          GROUP BY al.user_id, u.username
          ORDER BY count DESC
          LIMIT 10
        `, {
                    type: sequelize_1.QueryTypes.SELECT,
                    replacements: this.buildReplacements(baseWhere)
                });
                userStats = userStatsQuery.map(item => ({
                    userId: item.user_id,
                    username: item.username || 'Unknown',
                    count: parseInt(item.count)
                }));
            }
            const hourlyStatsQuery = await database_1.sequelize.query(`
        SELECT EXTRACT(HOUR FROM created_at) as hour, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
      `, {
                type: sequelize_1.QueryTypes.SELECT,
                replacements: this.buildReplacements(baseWhere)
            });
            const hourlyStats = Array.from({ length: 24 }, (_, i) => ({ hour: i, count: 0 }));
            hourlyStatsQuery.forEach(item => {
                const hour = parseInt(item.hour);
                const count = parseInt(item.count);
                if (hour >= 0 && hour < 24 && hourlyStats[hour]) {
                    hourlyStats[hour].count = count;
                }
            });
            const avgDurationQuery = await database_1.sequelize.query(`
        SELECT AVG(duration) as avg_duration
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)} AND duration IS NOT NULL
      `, {
                type: sequelize_1.QueryTypes.SELECT,
                replacements: this.buildReplacements(baseWhere)
            });
            const averageDuration = avgDurationQuery[0]?.avg_duration ?
                Math.round(parseFloat(avgDurationQuery[0].avg_duration)) : 0;
            const dailyStatsQuery = await database_1.sequelize.query(`
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `, {
                type: sequelize_1.QueryTypes.SELECT,
                replacements: this.buildReplacements(baseWhere)
            });
            const dailyStats = dailyStatsQuery.map(item => ({
                date: item.date,
                count: parseInt(item.count)
            }));
            return {
                totalLogs,
                successLogs,
                failedLogs,
                actionStats,
                resourceStats,
                dailyStats,
                userStats,
                hourlyStats,
                statusDistribution: {
                    success: successLogs,
                    failed: failedLogs,
                    pending: pendingLogs
                },
                averageDuration,
                topActions,
                topResources
            };
        }
        catch (error) {
            console.error('Failed to get detailed audit log stats:', error);
            throw new Error('Failed to get audit log statistics');
        }
    }
    static async getStats(userId, days = 30) {
        try {
            return await AuditLog_1.AuditLog.getStats(userId, days);
        }
        catch (error) {
            console.error('Failed to get audit log stats:', error);
            throw new Error('Failed to get audit log statistics');
        }
    }
    static async cleanupOldLogs(days = 90) {
        try {
            return await AuditLog_1.AuditLog.cleanupOldLogs(days);
        }
        catch (error) {
            console.error('Failed to cleanup old audit logs:', error);
            throw new Error('Failed to cleanup old audit logs');
        }
    }
    static async getLogById(id) {
        try {
            return await AuditLog_1.AuditLog.findByPk(id, {
                include: [
                    {
                        model: User_1.User,
                        as: 'user',
                        attributes: ['id', 'username', 'email']
                    }
                ]
            });
        }
        catch (error) {
            console.error('Failed to get audit log by id:', error);
            throw new Error('Failed to get audit log');
        }
    }
    static buildWhereClause(where) {
        const conditions = ['1=1'];
        if (where.userId !== undefined) {
            conditions.push('user_id = :userId');
        }
        if (where.resource) {
            conditions.push('resource = :resource');
        }
        if (where.action) {
            conditions.push('action = :action');
        }
        if (where.createdAt) {
            if (where.createdAt[sequelize_1.Op.gte]) {
                conditions.push('created_at >= :startDate');
            }
            if (where.createdAt[sequelize_1.Op.lte]) {
                conditions.push('created_at <= :endDate');
            }
        }
        return conditions.join(' AND ');
    }
    static buildReplacements(where) {
        const replacements = {};
        if (where.userId !== undefined) {
            replacements.userId = where.userId;
        }
        if (where.resource) {
            replacements.resource = where.resource;
        }
        if (where.action) {
            replacements.action = where.action;
        }
        if (where.createdAt) {
            if (where.createdAt[sequelize_1.Op.gte]) {
                replacements.startDate = where.createdAt[sequelize_1.Op.gte];
            }
            if (where.createdAt[sequelize_1.Op.lte]) {
                replacements.endDate = where.createdAt[sequelize_1.Op.lte];
            }
        }
        return replacements;
    }
}
exports.AuditLogService = AuditLogService;
exports.default = AuditLogService;
//# sourceMappingURL=auditLog.js.map
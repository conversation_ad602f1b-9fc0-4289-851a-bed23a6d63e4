import { Model, Optional } from 'sequelize';
export interface SettingsAttributes {
    id: number;
    userId: number;
    displayName?: string;
    avatar?: string;
    bio?: string;
    website?: string;
    location?: string;
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    itemsPerPage: number;
    emailNotifications: boolean;
    commentNotifications: boolean;
    systemNotifications: boolean;
    profileVisibility: 'public' | 'private';
    defaultPostVisibility: 'public' | 'private';
    showEmail: boolean;
    twoFactorEnabled: boolean;
    maxFileSize: number;
    allowedFileTypes: string[];
    autoGenerateThumbnail: boolean;
    defaultArticleVisibility: 'public' | 'private';
    enableComments: boolean;
    requireCommentApproval: boolean;
    enableArticleLikes: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface SettingsCreationAttributes extends Optional<SettingsAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Settings extends Model<SettingsAttributes, SettingsCreationAttributes> implements SettingsAttributes {
    id: number;
    userId: number;
    displayName?: string;
    avatar?: string;
    bio?: string;
    website?: string;
    location?: string;
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    itemsPerPage: number;
    emailNotifications: boolean;
    commentNotifications: boolean;
    systemNotifications: boolean;
    profileVisibility: 'public' | 'private';
    defaultPostVisibility: 'public' | 'private';
    showEmail: boolean;
    twoFactorEnabled: boolean;
    maxFileSize: number;
    allowedFileTypes: string[];
    autoGenerateThumbnail: boolean;
    defaultArticleVisibility: 'public' | 'private';
    enableComments: boolean;
    requireCommentApproval: boolean;
    enableArticleLikes: boolean;
    createdAt: Date;
    updatedAt: Date;
    static findByUserId(userId: number): Promise<Settings | null>;
    static upsertByUserId(userId: number, settingsData: Partial<SettingsAttributes>): Promise<Settings>;
    static getDefaultSettings(): Partial<SettingsAttributes>;
    static validateSettings(settingsData: Partial<SettingsAttributes>): {
        valid: boolean;
        errors: string[];
    };
    toJSON(): Omit<SettingsAttributes, never>;
}
//# sourceMappingURL=Settings.d.ts.map
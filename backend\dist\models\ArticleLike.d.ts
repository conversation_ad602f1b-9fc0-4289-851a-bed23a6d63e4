import { Model, Optional } from 'sequelize';
export interface ArticleLikeAttributes {
    id: number;
    articleId: number;
    userId: number;
    createdAt: Date;
}
export interface ArticleLikeCreationAttributes extends Optional<ArticleLikeAttributes, 'id' | 'createdAt'> {
}
export declare class ArticleLike extends Model<ArticleLikeAttributes, ArticleLikeCreationAttributes> implements ArticleLikeAttributes {
    id: number;
    articleId: number;
    userId: number;
    createdAt: Date;
    static toggleLike(articleId: number, userId: number): Promise<{
        liked: boolean;
        likeCount: number;
    }>;
    static isLikedByUser(articleId: number, userId: number): Promise<boolean>;
    static getArticleLikeCount(articleId: number): Promise<number>;
    static getUserLikedArticles(userId: number, limit?: number, offset?: number): Promise<{
        rows: ArticleLike[];
        count: number;
    }>;
    static getMostLikedArticles(limit?: number, days?: number): Promise<ArticleLike[]>;
    static getArticleLikers(articleId: number, limit?: number, offset?: number): Promise<{
        rows: ArticleLike[];
        count: number;
    }>;
}
//# sourceMappingURL=ArticleLike.d.ts.map
import { Request, Response, NextFunction } from 'express';
export interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare const requirePermission: (permissionName: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireRole: (roleName: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAllPermissions: (permissionNames: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAnyPermission: (permissionNames: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAllRoles: (roleNames: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAnyRole: (roleNames: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireSuperAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireEditor: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireOwnershipOrPermission: (getResourceOwnerId: (req: AuthenticatedRequest) => Promise<number | null>, adminPermission?: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserPermissions: (userId: number) => Promise<string[]>;
export declare const getUserRoles: (userId: number) => Promise<string[]>;
export declare const requirePermissionOrOwnership: (permissionName: string, modelName: string, ownerField: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=permission.d.ts.map
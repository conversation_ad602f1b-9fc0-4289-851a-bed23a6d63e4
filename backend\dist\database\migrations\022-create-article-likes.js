"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_likes', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('article_likes', ['article_id', 'user_id'], {
        unique: true
    });
    await queryInterface.addIndex('article_likes', ['article_id']);
    await queryInterface.addIndex('article_likes', ['user_id']);
    await queryInterface.addIndex('article_likes', ['created_at']);
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_likes');
};
exports.down = down;
//# sourceMappingURL=022-create-article-likes.js.map
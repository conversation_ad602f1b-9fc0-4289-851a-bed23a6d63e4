"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('users', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        username: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        email: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        password_hash: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '用户是否激活'
        },
        email_verified: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '邮箱是否已验证'
        },
        email_verified_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '邮箱验证时间'
        },
        last_login_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '最后登录时间'
        },
        password_reset_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '密码重置token'
        },
        password_reset_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '密码重置token过期时间'
        },
        email_verification_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '邮箱验证token'
        },
        email_verification_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '邮箱验证token过期时间'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('users', ['username']);
    }
    catch {
        console.log('Index users_username already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('users', ['email']);
    }
    catch {
        console.log('Index users_email already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('users');
};
exports.down = down;
//# sourceMappingURL=001-create-users.js.map
import { Model, Optional } from 'sequelize';
export interface ArticleMediaAttributes {
    id: number;
    articleId: number;
    mediaId: number;
    type: 'cover' | 'content' | 'gallery';
    sort: number;
    createdAt: Date;
}
export interface ArticleMediaCreationAttributes extends Optional<ArticleMediaAttributes, 'id' | 'sort' | 'createdAt'> {
}
export declare class ArticleMedia extends Model<ArticleMediaAttributes, ArticleMediaCreationAttributes> implements ArticleMediaAttributes {
    id: number;
    articleId: number;
    mediaId: number;
    type: 'cover' | 'content' | 'gallery';
    sort: number;
    createdAt: Date;
    static findByArticleId(articleId: number, type?: string): Promise<ArticleMedia[]>;
    static findByMediaId(mediaId: number): Promise<ArticleMedia[]>;
}
//# sourceMappingURL=ArticleMedia.d.ts.map
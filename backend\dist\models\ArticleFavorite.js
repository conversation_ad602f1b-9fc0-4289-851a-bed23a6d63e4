"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleFavorite = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class ArticleFavorite extends sequelize_1.Model {
    static async toggleFavorite(articleId, userId) {
        const existingFavorite = await this.findOne({
            where: { articleId, userId }
        });
        if (existingFavorite) {
            await existingFavorite.destroy();
            const favoriteCount = await this.count({ where: { articleId } });
            return { favorited: false, favoriteCount };
        }
        else {
            await this.create({ articleId, userId });
            const favoriteCount = await this.count({ where: { articleId } });
            return { favorited: true, favoriteCount };
        }
    }
    static async isFavoritedByUser(articleId, userId) {
        const favorite = await this.findOne({
            where: { articleId, userId }
        });
        return !!favorite;
    }
    static async getArticleFavoriteCount(articleId) {
        return this.count({
            where: { articleId }
        });
    }
    static async getUserFavoriteArticles(userId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { userId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: database_1.sequelize.models.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
                    include: [
                        {
                            model: database_1.sequelize.models.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        },
                        {
                            model: database_1.sequelize.models.Category,
                            as: 'category',
                            attributes: ['id', 'name', 'slug']
                        }
                    ]
                }
            ]
        });
    }
    static async getMostFavoritedArticles(limit = 10, days) {
        const whereClause = {};
        if (days) {
            const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
            whereClause.createdAt = {
                [database_1.sequelize.Op.gte]: startDate
            };
        }
        return this.findAll({
            attributes: [
                'articleId',
                [database_1.sequelize.fn('COUNT', database_1.sequelize.col('id')), 'favoriteCount']
            ],
            where: whereClause,
            group: ['articleId'],
            order: [[database_1.sequelize.literal('favoriteCount'), 'DESC']],
            limit,
            include: [
                {
                    model: database_1.sequelize.models.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt']
                }
            ]
        });
    }
    static async getArticleFavoriters(articleId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { articleId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: database_1.sequelize.models.User,
                    as: 'user',
                    attributes: ['id', 'username']
                }
            ]
        });
    }
}
exports.ArticleFavorite = ArticleFavorite;
ArticleFavorite.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'ArticleFavorite',
    tableName: 'article_favorites',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
        {
            unique: true,
            fields: ['article_id', 'user_id']
        },
        {
            fields: ['article_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['created_at']
        }
    ]
});
//# sourceMappingURL=ArticleFavorite.js.map
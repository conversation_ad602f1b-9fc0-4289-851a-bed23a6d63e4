"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleLike = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const Article_1 = require("./Article");
const User_1 = require("./User");
class ArticleLike extends sequelize_1.Model {
    static async toggleLike(articleId, userId) {
        const existingLike = await this.findOne({
            where: { articleId, userId }
        });
        if (existingLike) {
            await existingLike.destroy();
            const likeCount = await this.count({ where: { articleId } });
            return { liked: false, likeCount };
        }
        else {
            await this.create({ articleId, userId });
            const likeCount = await this.count({ where: { articleId } });
            return { liked: true, likeCount };
        }
    }
    static async isLikedByUser(articleId, userId) {
        const like = await this.findOne({
            where: { articleId, userId }
        });
        return !!like;
    }
    static async getArticleLikeCount(articleId) {
        return this.count({
            where: { articleId }
        });
    }
    static async getUserLikedArticles(userId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { userId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        }
                    ]
                }
            ]
        });
    }
    static async getMostLikedArticles(limit = 10, days) {
        const whereClause = {};
        if (days) {
            const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
            whereClause.createdAt = {
                [sequelize_1.Op.gte]: startDate
            };
        }
        return this.findAll({
            attributes: [
                'articleId',
                [database_1.sequelize.fn('COUNT', database_1.sequelize.col('id')), 'likeCount']
            ],
            where: whereClause,
            group: ['articleId'],
            order: [[database_1.sequelize.literal('likeCount'), 'DESC']],
            limit,
            include: [
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        }
                    ]
                }
            ]
        });
    }
    static async getArticleLikers(articleId, limit = 20, offset = 0) {
        return this.findAndCountAll({
            where: { articleId },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'username']
                }
            ]
        });
    }
}
exports.ArticleLike = ArticleLike;
ArticleLike.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'ArticleLike',
    tableName: 'article_likes',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
        {
            unique: true,
            fields: ['article_id', 'user_id']
        },
        {
            fields: ['article_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['created_at']
        }
    ]
});
//# sourceMappingURL=ArticleLike.js.map
{"version": 3, "file": "UserFollow.js", "sourceRoot": "", "sources": ["../../src/models/UserFollow.ts"], "names": [], "mappings": ";;;AAAA,yCAAsD;AACtD,iDAA8C;AAqB9C,MAAa,UAAW,SAAQ,iBAAyD;IAShF,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,WAAmB;QAEtE,IAAI,UAAU,KAAK,WAAW,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC3B,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACxC,KAAK,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE;SACnC,CAAC,CAAA;QAEF,IAAI,cAAc,EAAE,CAAC;YAEnB,MAAM,cAAc,CAAC,OAAO,EAAE,CAAA;YAC9B,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACxD,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC;aACtC,CAAC,CAAA;YACF,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,CAAA;QAC5D,CAAC;aAAM,CAAC;YAEN,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAA;YAC9C,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACxD,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC;aACtC,CAAC,CAAA;YACF,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE,cAAc,EAAE,CAAA;QAC3D,CAAC;IACH,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,WAAmB;QACrE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YAChC,KAAK,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE;SACnC,CAAC,CAAA;QACF,OAAO,CAAC,CAAC,MAAM,CAAA;IACjB,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAA;IACJ,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAA;IACJ,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE,SAAiB,CAAC;QACrF,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;YAC9B,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9B,KAAK;YACL,MAAM;YACN,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAS,CAAC,MAAM,CAAC,IAAI;oBAC5B,EAAE,EAAE,UAAU;oBACd,UAAU,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/B;aACF;SACF,CAAC,CAAA;IACJ,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE,SAAiB,CAAC;QACrF,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9B,KAAK;YACL,MAAM;YACN,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAS,CAAC,MAAM,CAAC,IAAI;oBAC5B,EAAE,EAAE,WAAW;oBACf,UAAU,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/B;aACF;SACF,CAAC,CAAA;IACJ,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE,SAAiB,CAAC;QAEzF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QAEF,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;QAEtD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,KAAK,EAAE;gBACL,UAAU,EAAE,EAAE,CAAC,oBAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE;gBAC/C,WAAW,EAAE,MAAM;aACpB;YACD,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9B,KAAK;YACL,MAAM;YACN,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAS,CAAC,MAAM,CAAC,IAAI;oBAC5B,EAAE,EAAE,UAAU;oBACd,UAAU,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/B;aACF;SACF,CAAC,CAAA;IACJ,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,QAAgB,EAAE;QAExE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACvC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QAEF,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;QAE1D,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAA;QACX,CAAC;QAGD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,UAAU,EAAE,EAAE,CAAC,oBAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE;gBAC/C,WAAW,EAAE,EAAE,CAAC,oBAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;aAC3C;YACD,UAAU,EAAE;gBACV,aAAa;gBACb,CAAC,oBAAS,CAAC,EAAE,CAAC,OAAO,EAAE,oBAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,iBAAiB,CAAC;aAChE;YACD,KAAK,EAAE,CAAC,aAAa,CAAC;YACtB,KAAK,EAAE,CAAC,CAAC,oBAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,MAAM,CAAC,CAAC;YACvD,KAAK;YACL,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAS,CAAC,MAAM,CAAC,IAAI;oBAC5B,EAAE,EAAE,WAAW;oBACf,UAAU,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/B;aACF;SACF,CAAC,CAAA;QAGF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QAEF,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;QAEpE,OAAO,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAClC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAC/C,CAAA;IACH,CAAC;CACF;AA5LD,gCA4LC;AAKD,UAAU,CAAC,IAAI,CACb;IACE,EAAE,EAAE;QACF,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,IAAI;QACnB,SAAS,EAAE,KAAK;KACjB;IACD,UAAU,EAAE;QACV,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,aAAa;QACpB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;KACF;IACD,WAAW,EAAE;QACX,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,cAAc;QACrB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;KACF;IACD,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,YAAY;KACpB;CACF,EACD;IACE,SAAS,EAAT,oBAAS;IACT,SAAS,EAAE,YAAY;IACvB,SAAS,EAAE,cAAc;IACzB,UAAU,EAAE,IAAI;IAChB,SAAS,EAAE,KAAK;IAChB,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE;QACP;YACE,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;SACxC;QACD;YACE,MAAM,EAAE,CAAC,aAAa,CAAC;SACxB;QACD;YACE,MAAM,EAAE,CAAC,cAAc,CAAC;SACzB;QACD;YACE,MAAM,EAAE,CAAC,YAAY,CAAC;SACvB;KACF;CACF,CACF,CAAA"}
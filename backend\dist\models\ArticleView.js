"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleView = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const Article_1 = require("./Article");
class ArticleView extends sequelize_1.Model {
    static async recordView(articleId, ipAddress, userId, userAgent) {
        const recentView = await this.findOne({
            where: {
                articleId,
                ipAddress,
                ...(userId && { userId }),
                viewedAt: {
                    [sequelize_1.Op.gte]: new Date(Date.now() - 30 * 60 * 1000)
                }
            }
        });
        if (!recentView) {
            const createData = {
                articleId,
                ipAddress,
                viewedAt: new Date()
            };
            if (userId !== undefined) {
                createData.userId = userId;
            }
            if (userAgent !== undefined) {
                createData.userAgent = userAgent;
            }
            return this.create(createData);
        }
        return recentView;
    }
    static async getArticleViewCount(articleId) {
        return this.count({
            where: { articleId },
            distinct: true,
            col: 'ip_address'
        });
    }
    static async getArticleUniqueViewers(articleId) {
        return this.count({
            where: { articleId },
            distinct: true,
            col: 'ip_address'
        });
    }
    static async getUserViewHistory(userId, limit = 20) {
        return this.findAll({
            where: { userId },
            order: [['viewedAt', 'DESC']],
            limit,
            include: [
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt']
                }
            ]
        });
    }
    static async getPopularArticles(limit = 10, days = 7) {
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        return this.findAll({
            attributes: [
                'articleId',
                [database_1.sequelize.fn('COUNT', database_1.sequelize.fn('DISTINCT', database_1.sequelize.col('ip_address'))), 'viewCount']
            ],
            where: {
                viewedAt: {
                    [sequelize_1.Op.gte]: startDate
                }
            },
            group: ['articleId'],
            order: [[database_1.sequelize.literal('viewCount'), 'DESC']],
            limit,
            include: [
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt']
                }
            ]
        });
    }
}
exports.ArticleView = ArticleView;
ArticleView.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    ipAddress: {
        type: sequelize_1.DataTypes.STRING(45),
        allowNull: false,
        field: 'ip_address'
    },
    userAgent: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'user_agent'
    },
    viewedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        field: 'viewed_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'ArticleView',
    tableName: 'article_views',
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['article_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['ip_address']
        },
        {
            fields: ['viewed_at']
        },
        {
            fields: ['article_id', 'ip_address', 'viewed_at']
        }
    ]
});
//# sourceMappingURL=ArticleView.js.map
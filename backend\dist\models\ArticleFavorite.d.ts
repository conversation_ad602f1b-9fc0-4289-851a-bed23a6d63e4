import { Model, Optional } from 'sequelize';
export interface ArticleFavoriteAttributes {
    id: number;
    articleId: number;
    userId: number;
    createdAt: Date;
}
export interface ArticleFavoriteCreationAttributes extends Optional<ArticleFavoriteAttributes, 'id' | 'createdAt'> {
}
export declare class ArticleFavorite extends Model<ArticleFavoriteAttributes, ArticleFavoriteCreationAttributes> implements ArticleFavoriteAttributes {
    id: number;
    articleId: number;
    userId: number;
    createdAt: Date;
    static toggleFavorite(articleId: number, userId: number): Promise<{
        favorited: boolean;
        favoriteCount: number;
    }>;
    static isFavoritedByUser(articleId: number, userId: number): Promise<boolean>;
    static getArticleFavoriteCount(articleId: number): Promise<number>;
    static getUserFavoriteArticles(userId: number, limit?: number, offset?: number): Promise<{
        rows: ArticleFavorite[];
        count: number;
    }>;
    static getMostFavoritedArticles(limit?: number, days?: number): Promise<ArticleFavorite[]>;
    static getArticleFavoriters(articleId: number, limit?: number, offset?: number): Promise<{
        rows: ArticleFavorite[];
        count: number;
    }>;
}
//# sourceMappingURL=ArticleFavorite.d.ts.map
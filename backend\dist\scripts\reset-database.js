"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetDatabase = resetDatabase;
const database_1 = require("../config/database");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
async function runFreshMigrations() {
    const migrationsDir = path_1.default.join(__dirname, '../database/migrations');
    await database_1.sequelize.query(`
    CREATE TABLE IF NOT EXISTS migrations (
      name VARCHAR(255) NOT NULL,
      executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (name)
    ) ENGINE=InnoDB;
  `);
    const migrationFiles = fs_1.default.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.ts'))
        .sort();
    console.log(`Found ${migrationFiles.length} migration files`);
    for (const file of migrationFiles) {
        const migrationName = file.replace('.ts', '');
        console.log(`Running migration: ${migrationName}`);
        try {
            const migrationPath = path_1.default.join(migrationsDir, file);
            const migration = require(migrationPath);
            await migration.up(database_1.sequelize.getQueryInterface());
            await database_1.sequelize.query('INSERT INTO migrations (name) VALUES (?)', { replacements: [migrationName] });
            console.log(`✅ Migration ${migrationName} completed`);
        }
        catch (error) {
            console.error(`❌ Migration ${migrationName} failed:`, error);
            throw error;
        }
    }
}
async function resetDatabase() {
    try {
        console.log('🚀 Starting database reset...');
        console.log('==================================================');
        console.log(`📊 Database: ${process.env.DB_NAME}`);
        console.log(`🏠 Host: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
        console.log(`👤 User: ${process.env.DB_USER}`);
        console.log('');
        console.log('🔍 Testing database connection...');
        await database_1.sequelize.authenticate();
        console.log('✅ Database connection successful!');
        console.log('');
        console.log('📋 Getting all tables...');
        const [tables] = await database_1.sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME}' 
      AND TABLE_TYPE = 'BASE TABLE'
    `);
        if (tables.length > 0) {
            console.log(`Found ${tables.length} tables to drop:`);
            tables.forEach(table => console.log(`  - ${table.TABLE_NAME}`));
            console.log('');
            console.log('🔓 Disabling foreign key checks...');
            await database_1.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
            console.log('🗑️ Dropping all tables...');
            for (const table of tables) {
                console.log(`  Dropping table: ${table.TABLE_NAME}`);
                await database_1.sequelize.query(`DROP TABLE IF EXISTS \`${table.TABLE_NAME}\``);
            }
            console.log('🔒 Re-enabling foreign key checks...');
            await database_1.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
            console.log('✅ All tables dropped successfully!');
        }
        else {
            console.log('ℹ️ No tables found to drop.');
        }
        console.log('');
        console.log('📦 Running fresh migrations...');
        await runFreshMigrations();
        console.log('');
        console.log('==================================================');
        console.log('🎉 Database reset completed successfully!');
    }
    catch (error) {
        console.error('❌ Database reset failed:', error);
        throw error;
    }
    finally {
        await database_1.sequelize.close();
        console.log('Database connection closed');
    }
}
if (require.main === module) {
    resetDatabase()
        .then(() => {
        console.log('✅ Reset completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('❌ Reset failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=reset-database.js.map